<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Intelligence Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --bg-primary: #0A0B0D;
            --bg-secondary: #111317;
            --bg-tertiary: #1A1D21;
            --bg-elevated: #1E2227;
            --bg-hover: #252A31;
            --bg-glass: rgba(30, 34, 39, 0.8);
            
            --text-primary: #F7F8F8;
            --text-secondary: #A1A7B0;
            --text-tertiary: #6B7280;
            
            --accent-primary: #5E5CE6;
            --accent-secondary: #BF5AF2;
            --accent-success: #30D158;
            --accent-warning: #FFD60A;
            --accent-danger: #FF453A;
            --accent-info: #64D2FF;
            
            --border-primary: rgba(255, 255, 255, 0.08);
            --border-secondary: rgba(255, 255, 255, 0.04);
            
            --gradient-primary: linear-gradient(135deg, #5E5CE6 0%, #BF5AF2 100%);
            --gradient-secondary: linear-gradient(135deg, #30D158 0%, #34C759 100%);
            --gradient-tertiary: linear-gradient(135deg, #FFD60A 0%, #FF9F0A 100%);
            
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 10px 40px rgba(0, 0, 0, 0.5);
            --shadow-glow: 0 0 40px rgba(94, 92, 230, 0.3);
            
            --blur-sm: 8px;
            --blur-md: 16px;
            --blur-lg: 24px;
            
            --transition-fast: 150ms cubic-bezier(0.2, 0, 0, 1);
            --transition-base: 300ms cubic-bezier(0.2, 0, 0, 1);
            --transition-slow: 500ms cubic-bezier(0.2, 0, 0, 1);
            --spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Display', system-ui, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 14px;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--bg-hover);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-tertiary);
        }

        /* Layout */
        .app-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 240px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-primary);
            padding: 1.5rem 1rem;
            display: flex;
            flex-direction: column;
            gap: 2rem;
            position: relative;
            z-index: 100;
            transition: transform var(--transition-base), opacity var(--transition-base);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: var(--gradient-primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.125rem;
            box-shadow: var(--shadow-glow);
            position: relative;
            overflow: hidden;
        }

        .logo-icon::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                to bottom right,
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0) 80%
            );
            transform: rotate(30deg);
        }

        .logo-text {
            font-weight: 600;
            font-size: 1.125rem;
        }

        .nav-section {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .nav-label {
            font-size: 0.75rem;
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0.5rem 0.75rem;
            font-weight: 600;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.625rem 0.75rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all var(--transition-base);
            position: relative;
            color: var(--text-secondary);
        }

        .nav-item:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
            transform: translateX(2px);
        }

        .nav-item.active {
            background: rgba(94, 92, 230, 0.15);
            color: var(--text-primary);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: var(--accent-primary);
            border-radius: 0 2px 2px 0;
        }

        .nav-icon {
            font-size: 1.125rem;
            transition: transform var(--transition-base);
        }

        .nav-item:hover .nav-icon {
            transform: scale(1.15);
        }

        .nav-count {
            margin-left: auto;
            font-size: 0.75rem;
            padding: 0.125rem 0.5rem;
            background: var(--bg-elevated);
            border-radius: 999px;
            font-weight: 600;
            transition: all var(--transition-base);
        }

        .nav-item:hover .nav-count {
            background: var(--accent-primary);
            color: white;
            transform: scale(1.1);
        }

        .nav-item.active .nav-count {
            background: var(--accent-primary);
            color: white;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        /* Header */
        .header {
            background: var(--bg-glass);
            backdrop-filter: blur(var(--blur-md));
            -webkit-backdrop-filter: blur(var(--blur-md));
            border-bottom: 1px solid var(--border-primary);
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 90;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .header-title {
            font-size: 1.25rem;
            font-weight: 600;
            position: relative;
        }

        .header-title::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 60%;
            height: 2px;
            background: var(--gradient-primary);
            border-radius: 2px;
            opacity: 0.8;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-container {
            position: relative;
            width: 320px;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 10px;
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all var(--transition-base);
        }

        .search-input::placeholder {
            color: var(--text-tertiary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px rgba(94, 92, 230, 0.2);
            transform: translateY(-1px);
        }

        .search-icon {
            position: absolute;
            left: 0.875rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-tertiary);
            transition: all var(--transition-base);
        }

        .search-input:focus ~ .search-icon {
            color: var(--accent-primary);
        }

        .header-button {
            padding: 0.75rem 1.25rem;
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .header-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .header-button:hover {
            background: #4F4CE0;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .header-button:hover::before {
            left: 100%;
        }

        .header-button:active {
            transform: translateY(0);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .user-menu:hover {
            background: var(--bg-hover);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: var(--gradient-primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            position: relative;
            overflow: hidden;
            transition: all var(--transition-base);
        }

        .user-menu:hover .user-avatar {
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(94, 92, 230, 0.4);
        }

        /* Content Area */
        .content {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
            position: relative;
        }
        
        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            gap: 1.25rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: 16px;
            padding: 1.25rem 1.5rem;
            position: relative;
            overflow: hidden;
            transition: all var(--transition-base);
            display: flex;
            flex-direction: column;
            gap: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            will-change: transform, box-shadow, border-color;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity var(--transition-base);
        }

        .stat-card:hover {
            border-color: var(--accent-primary);
            transform: translateY(-4px) scale(1.02);
            box-shadow: var(--shadow-md), 0 0 0 1px rgba(94, 92, 230, 0.2);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-top {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            background: var(--bg-elevated);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 0.75rem;
            transition: all var(--transition-base);
            border: 1px solid var(--border-secondary);
        }

        .stat-card:hover .stat-icon {
            background: rgba(94, 92, 230, 0.15);
            transform: scale(1.1) rotate(5deg);
            border-color: var(--accent-primary);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .stat-content {
            flex: 1;
            min-width: 0;
        }

        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            line-height: 1.2;
            transition: all var(--transition-base);
        }

        .stat-card:hover .stat-value {
            transform: scale(1.05);
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            margin-top: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 999px;
            width: fit-content;
            transition: all var(--transition-base);
        }

        .stat-change.positive {
            color: var(--accent-success);
            background: rgba(48, 209, 88, 0.15);
        }

        .stat-change.negative {
            color: var(--accent-danger);
            background: rgba(255, 69, 58, 0.15);
        }

        .stat-card:hover .stat-change {
            transform: translateX(5px);
        }

        /* Task List */
        .task-list-container {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: 16px;
            overflow: hidden;
            margin-top: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform var(--transition-base), box-shadow var(--transition-base);
        }
        
        .task-list-container:hover {
            box-shadow: var(--shadow-md);
        }

        .task-list-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-primary);
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
        }

        .task-list-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .task-list-title::after {
            content: '';
            display: block;
            width: 32px;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: 3px;
        }

        .task-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            max-width: 100%;
        }

        .filter-chip {
            padding: 0.625rem 0.875rem;
            background: var(--bg-tertiary);
            border: 1.5px solid var(--border-primary);
            border-radius: 12px;
            font-size: 0.8125rem;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .filter-chip:hover {
            border-color: var(--accent-primary);
            color: var(--text-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .filter-chip.active {
            background: rgba(94, 92, 230, 0.15);
            border-color: var(--accent-primary);
            color: var(--accent-primary);
            font-weight: 600;
            box-shadow: 0 0 0 3px rgba(94, 92, 230, 0.2);
        }

        .filter-chip span {
            position: relative;
            z-index: 1;
        }

        .filter-count {
            padding: 0.125rem 0.375rem;
            background: var(--bg-elevated);
            border-radius: 6px;
            font-size: 0.6875rem;
            font-weight: 700;
            color: var(--text-primary);
            transition: all var(--transition-base);
        }

        .filter-chip:hover .filter-count {
            background: var(--accent-primary);
            color: white;
            transform: scale(1.1);
        }

        .filter-chip.active .filter-count {
            background: var(--accent-primary);
            color: white;
        }
