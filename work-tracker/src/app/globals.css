@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #0f172a;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #475569;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;
  --radius: 0.75rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Modern Card Styles */
.modern-card {
  @apply bg-white rounded-2xl shadow-sm border border-gray-200 p-8;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
  border: 1px solid rgba(229, 231, 235, 0.6);
}

.stats-card {
  @apply relative overflow-hidden rounded-2xl p-6 text-white shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

/* Button Styles */
.btn-primary {
  @apply px-6 py-3 rounded-xl font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply px-6 py-3 rounded-xl font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 focus:ring-gray-500;
}

.btn-ghost {
  @apply px-6 py-3 rounded-xl font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-transparent text-gray-600 hover:bg-gray-100 hover:text-gray-900;
}

/* Table Styles */
.modern-table {
  @apply w-full border-collapse;
}

.modern-table th {
  @apply text-left py-4 px-6 font-semibold text-gray-700 border-b border-gray-200 bg-gray-50/50;
}

.modern-table td {
  @apply py-4 px-6 border-b border-gray-100;
}

.modern-table tr {
  @apply hover:bg-gray-50/50 transition-colors duration-150;
}

/* Progress Bar */
.progress-container {
  @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-bar {
  @apply h-full rounded-full transition-all duration-500 ease-out;
}

/* Status Badge */
.status-badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
}

/* Input Styles */
.modern-input {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white;
}

.modern-select {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white;
}

/* Utility Classes */
.glass-effect {
  @apply backdrop-blur-md bg-white border border-white;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* Gradient Backgrounds */
.gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.gradient-orange {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .modern-card {
    @apply p-4 rounded-xl;
  }

  .modern-table th,
  .modern-table td {
    @apply py-3 px-4 text-sm;
  }

  .btn-primary,
  .btn-secondary,
  .btn-ghost {
    @apply px-4 py-2.5 text-sm;
  }
}

/* Touch Optimizations */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-105:hover {
    transform: none;
  }

  .hover\:shadow-xl:hover {
    box-shadow: none;
  }
}

/* Safe Area for iOS */
@supports (padding: max(0px)) {
  .safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
}

/* Hide scrollbar for horizontal tabs */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Smooth snap scrolling for mobile tabs */
.snap-x {
  scroll-snap-type: x mandatory;
}

.snap-start {
  scroll-snap-align: start;
}

/* Touch improvements for mobile */
@media (hover: none) and (pointer: coarse) {
  .snap-x {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}
